[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[notification_handler] Type: , Message: ''
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[notification_handler] Args received: ['/Users/<USER>/Desktop/github/next13-clarify/remocode/v2/hooks/notification_handler.py', '', '']
[notification_handler] Env vars: CLAUDE_NOTIFICATION_TYPE='NOT_SET', CLAUDE_NOTIFICATION_MESSAGE='NOT_SET'
[notification_handler] Processed: Type: '', Message: ''
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Tool: Unknown, Input: {}, Message: 
[pretool_guard] Hook Event: PreToolUse, Tool: Read, Input: {'file_path': '/tmp/test.txt'}, Session: test-session-123
Raw payload: {
  "session_id": "test-session-123",
  "transcript_path": "/tmp/test-transcript.jsonl",
  "cwd": "/Users/<USER>/Desktop/github/next13-clarify/remocode/v2",
  "hook_event_name": "PreToolUse",
  "tool_name": "Read",
  "tool_input": {
    "file_path": "/tmp/test.txt"
  }
}
[pretool_guard] Hook Event: PreToolUse, Tool: Write, Input: {'file_path': '/tmp/test.txt', 'content': 'test'}, Session: test-session-123
Raw payload: {
  "session_id": "test-session-123",
  "transcript_path": "/tmp/test-transcript.jsonl",
  "cwd": "/Users/<USER>/Desktop/github/next13-clarify/remocode/v2",
  "hook_event_name": "PreToolUse",
  "tool_name": "Write",
  "tool_input": {
    "file_path": "/tmp/test.txt",
    "content": "test"
  }
}
[pretool_guard] Hook Event: PreToolUse, Tool: WebFetch, Input: {'url': 'https://example.com'}, Session: test-session-123
Raw payload: {
  "session_id": "test-session-123",
  "transcript_path": "/tmp/test-transcript.jsonl",
  "cwd": "/Users/<USER>/Desktop/github/next13-clarify/remocode/v2",
  "hook_event_name": "PreToolUse",
  "tool_name": "WebFetch",
  "tool_input": {
    "url": "https://example.com"
  }
}
[pretool_guard] Hook Event: PreToolUse, Tool: Bash, Input: {'command': 'ls -la'}, Session: test-session-123
Raw payload: {
  "session_id": "test-session-123",
  "transcript_path": "/tmp/test-transcript.jsonl",
  "cwd": "/Users/<USER>/Desktop/github/next13-clarify/remocode/v2",
  "hook_event_name": "PreToolUse",
  "tool_name": "Bash",
  "tool_input": {
    "command": "ls -la"
  }
}
[pretool_guard] Hook Event: PreToolUse, Tool: Bash, Input: {'command': 'npm run build'}, Session: test-session-123
Raw payload: {
  "session_id": "test-session-123",
  "transcript_path": "/tmp/test-transcript.jsonl",
  "cwd": "/Users/<USER>/Desktop/github/next13-clarify/remocode/v2",
  "hook_event_name": "PreToolUse",
  "tool_name": "Bash",
  "tool_input": {
    "command": "npm run build"
  }
}
[pretool_guard] Hook Event: PreToolUse, Tool: Bash, Input: {'command': 'git status'}, Session: test-session-123
Raw payload: {
  "session_id": "test-session-123",
  "transcript_path": "/tmp/test-transcript.jsonl",
  "cwd": "/Users/<USER>/Desktop/github/next13-clarify/remocode/v2",
  "hook_event_name": "PreToolUse",
  "tool_name": "Bash",
  "tool_input": {
    "command": "git status"
  }
}
[pretool_guard] Hook Event: PreToolUse, Tool: UnknownTool, Input: {'param': 'value'}, Session: test-session-123
Raw payload: {
  "session_id": "test-session-123",
  "transcript_path": "/tmp/test-transcript.jsonl",
  "cwd": "/Users/<USER>/Desktop/github/next13-clarify/remocode/v2",
  "hook_event_name": "PreToolUse",
  "tool_name": "UnknownTool",
  "tool_input": {
    "param": "value"
  }
}
[notification_handler] JSON input: {
  "session_id": "test-session-123",
  "transcript_path": "/tmp/test-transcript.jsonl",
  "cwd": "/Users/<USER>/Desktop/github/next13-clarify/remocode/v2",
  "hook_event_name": "Notification",
  "message": "Claude needs your permission to use Bash"
}
[notification_handler] JSON input: {
  "session_id": "test-session-123",
  "transcript_path": "/tmp/test-transcript.jsonl",
  "cwd": "/Users/<USER>/Desktop/github/next13-clarify/remocode/v2",
  "hook_event_name": "Notification",
  "message": "Claude is waiting for your input"
}
[notification_handler] JSON input: {
  "session_id": "test-session-123",
  "transcript_path": "/tmp/test-transcript.jsonl",
  "cwd": "/Users/<USER>/Desktop/github/next13-clarify/remocode/v2",
  "hook_event_name": "Notification",
  "message": "Would you like to proceed with the plan?"
}
[notification_handler] JSON input: {
  "session_id": "test-session-123",
  "transcript_path": "/tmp/test-transcript.jsonl",
  "cwd": "/Users/<USER>/Desktop/github/next13-clarify/remocode/v2",
  "hook_event_name": "Notification",
  "message": "Generic notification message"
}
