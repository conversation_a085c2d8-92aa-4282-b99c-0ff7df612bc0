# RemoCode · Telegram Integration Specification v1.0

## 1 · Purpose

RemoCode enables developers to **drive an AI-powered coding session from either the local terminal _or_ Telegram**—whichever is more convenient at the moment—without sacrificing real-time feedback, auditability, or control.

The system mirrors all CLI output to a dedicated Telegram _forum topic_ for the active Git branch while racing to accept input from **the first surface that replies** (keyboard or Telegram button). It must be robust enough for daily professional workflows and extensible to future chat surfaces (e.g. Slack).

---

## 2 · Scope

### 2.1 Goals

1. Full bi-directional relay between the Claude-Code CLI (child process) and Telegram.
2. "First-reply-wins" race condition for numbered menus and free-form prompts.
3. Stateless recovery: restart the wrapper and continue exactly where you left off.
4. Slash-command palette for helper tasks (`/handy`, `/cost`, future `/format`).
5. Zero external infrastructure—everything runs on the developer’s workstation.

### 2.2 Non-Goals (v1.0)

• Multi-user concurrency on the same topic.
• Cloud deployment or shared runners.
• GUI dashboards (deferred to v2).

---

## 3 · High-Level Architecture

```
┌────────────┐         stdout/stderr         ┌─────────────┐
│  Terminal  │ <──────────────────────────── │ Claude CLI  │
│   (tty)    │ ────────────────────────────► │  Process    │
└────┬───────┘             stdin             └────┬────────┘
     │                                         ▲  │
     │                   asyncio streams       │  │ child.write()
     ▼                                         │  │
┌──────────────────────────────┐              │  │
│   remocode/run.py (wrapper)  │──────────────┘  │
│ • multiplex IO               │  HTTP/S         │
│ • detect menus & timeouts    │─────────────────┘
│ • persist session state      │
└──────────┬───────────────────┘
           │
           ▼
┌────────────────────────────────────────────┐
│ Telegram Topic (per-branch forum thread)   │
│ • mirrors logs (silent by default)         │
│ • inline buttons for menu choices          │
│ • slash-commands (/handy, /cost …)         │
└────────────────────────────────────────────┘
```

### Key Design Choices

- **Async `subprocess` vs `pexpect`** — community experience (e.g. [`claude-code-telegram`](https://github.com/ybeglov/claude-code-telegram)) shows `pexpect` mis-detects EOFs on rich TUI output. `asyncio.create_subprocess_exec` avoids this while still giving non-blocking pipes.
- **Per-branch forum topics** — mirrors Git branch names to isolated Telegram threads, inspired by [`telegram-manual-approval`](https://github.com/marketplace/actions/telegram-manual-approval).
- **Button reuse** — borrow inline-button helper from the OSS projects above, but add idempotent edits to comply with Telegram 30 msg/s rate limit.

---

## 4 · Components

| Component                  | Language              | Responsibilities                                                                                                                                                                          |
| -------------------------- | --------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `remocode/run.py`          | Python 3.11 / asyncio | Spawn Claude CLI, mirror stdout, implement race logic, detect numbered menus via `MENU_RE = r"^\s*\d+\)"`, marshal messages to an `asyncio.Queue`.                                        |
| `remocode/telegram_bot.py` | Python                | Use `python-telegram-bot v20` to:<br>• post log lines to topic (silent)<br>• render inline buttons for menus<br>• forward button text → queue<br>• implement `/handy` & `/cost` commands. |
| `remocode/state.json`      | JSON (≤ 1 MiB)        | Rolling buffer of the last 500 log lines & the current Claude prompt/menu so the wrapper can resume cleanly.                                                                              |
| `.env`                     | dotenv                | `TG_TOKEN`, `TG_CHAT`, optional `OPENAI_API_KEY`, etc. (already provisioned).                                                                                                             |
| `remocode/logging.py`      | Python                | Truncate `remocode.log` to 500 lines on start-up.                                                                                                                                         |

### Helper Scripts (re-usable OSS)

- **Button rendering** — adapt [`claude-code-telegram`](https://github.com/ybeglov/claude-code-telegram/blob/main/src/buttons.py).
- **Topic creation** — copy logic from Telegram Bot API example `createForumTopic`.
- **Cost parser** — borrow token parsing regex from [`Code Review GPT`](https://github.com/marketplace/actions/code-review-gpt) to compute $ spend.

---

## 5 · Interaction Flow

1. **Startup**
   1. Read `state.json` → replay buffer to terminal & Telegram (if present).
   2. Spawn Claude CLI (`claude --mode acceptEdits`).
   3. Create / reuse Telegram topic for current Git branch.
2. **Streaming**
   - `stdout/stderr` lines → terminal **and** Telegram (silent message edits every 1 s batch).
3. **Menu Detection & Race**
   - Whenever a line matches `MENU_RE`, wrapper publishes inline buttons.
   - It then awaits **whichever arrives first**:
     - keyboard input via `sys.stdin.readline()`; or
     - button callback via `Queue.get()`.
   - The winner is forwarded to Claude and buttons are dissolved.
4. **Timeout** (default 5 min)
   - If no reply, wrapper edits the Telegram message to "⏳ Timed out – sent _skip_" and writes `skip\n` to Claude.
5. **Helper Commands**
   - `/handy` opens palette (Gemini review, Run Jest). Selection executes a subprocess; stdout is streamed back to topic and Claude.
   - `/cost` parses last token usage and replies with cost estimate.
6. **Shutdown & Resume**
   - On SIGINT or unexpected exit, wrapper serialises final `state.json` and cleanly ends the child process.
   - Next launch resumes from that snapshot.

---

## 6 · Phased Delivery Plan

| Phase  | Milestone              | Key Tasks                                                                                                                                         | Target Owner | Duration |
| ------ | ---------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------- | ------------ | -------- |
| **P1** | _Async Core_           | • Implement `run.py` with bidirectional streams.<br>• Mirror logs to terminal + Telegram.<br>• Basic menu detection.<br>• Manual topic selection. | dev-A        | 3 days   |
| **P2** | _Interactive Controls_ | • Inline buttons / race logic.<br>• `/handy` & `/cost` commands.<br>• Config enum `Mode = {DUAL, TERMINAL, TELEGRAM}`.                            | dev-B        | 4 days   |
| **P3** | _Reliability_          | • 5-minute timeout & auto-skip.<br>• `state.json` persistence + resume.<br>• Log rotation & error retry (3×).                                     | dev-A        | 3 days   |
| **P4** | _Branch-Aware Topics_  | • Auto-create forum topic per `git rev-parse --abbrev-ref HEAD`.<br>• Multi-topic cache & cleanup.                                                | dev-B        | 2 days   |
| **P5** | _Polish & Docs_        | • Security review (secret redaction).<br>• README quick-start.<br>• Unit tests (pytest + fixtures).<br>• Road-map for Slack adapter & CI runner.  | both         | 2 days   |

_Total calendar time ~14 days with 2 alternating developers._

---

## 7 · Risks & Mitigations

| Risk                                    | Impact               | Mitigation                                                                   |
| --------------------------------------- | -------------------- | ---------------------------------------------------------------------------- |
| Telegram API rate limits (30 msg/s)     | Loss of messages     | Batch edits; coalesce logs every second; reuse same message ID.              |
| Claude prompt format changes            | Menu detection fails | Isolate regex in single util; add e2e test fixtures.                         |
| Long-running helper tasks block wrapper | Session stalls       | Spawn helpers with `asyncio.create_subprocess_exec`, enforce 10 min timeout. |
| Secret leakage in logs                  | Security             | Mask via `re.sub(r'(sk-[\w-]{10,})', 'sk-***')` before publishing.           |
| Network outage                          | Telegram unreachable | Queue messages locally; retry 3 times, then fall back to terminal-only mode. |

---

## 8 · Future Work (post-v1.0)

1. **GitHub Runner Mode** — attach wrapper to CI jobs for manual approvals.
2. **Slack Adapter** — swap Telegram client for Slack Web-API.
3. **Token Budget Guard** — abort run when >$X cost predicted.
4. **/format Quick-Action** — run Prettier/Black and apply patch.
5. **Web Dashboard** — render artefacts & logs in a Next.js page.

---

## 9 · References

- [`claude-code-telegram`](https://github.com/ybeglov/claude-code-telegram) — inline button patterns.
- [`telegram-manual-approval`](https://github.com/marketplace/actions/telegram-manual-approval) — per-branch topics & first-reply flow.
- Blog _Claude Code hooks to Telegram_ — webhook inspiration.
- [`Code Review GPT`](https://github.com/marketplace/actions/code-review-gpt) — token cost parsing.

---

_Last updated {{DATE_GENERATED_BY_AI}}._
